# Documentation API ClockIn

## Base URL
```
http://localhost:8001/api
```

## Authentification

Toutes les routes protégées nécessitent un token Bearer dans l'en-tête :
```
Authorization: Bearer {token}
```

## Endpoints

### 1. Authentification

#### POST /login
Authentifie un utilisateur et retourne un token.

**Paramètres :**
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**Réponse succès (200) :**
```json
{
    "success": true,
    "message": {
        "en": "Login successful",
        "fr": "Connexion réussie",
        "ar": "تم تسجيل الدخول بنجاح"
    },
    "data": {
        "user": {
            "id": 1,
            "name": "Admin ClockIn",
            "email": "<EMAIL>",
            "role": "admin"
        },
        "token": "1|abc123...",
        "token_type": "Bearer"
    }
}
```

#### POST /logout
Déconnecte l'utilisateur (révoque le token).

**Headers requis :** Authorization: Bearer {token}

**Réponse succès (200) :**
```json
{
    "success": true,
    "message": {
        "en": "Logout successful",
        "fr": "Déconnexion réussie",
        "ar": "تم تسجيل الخروج بنجاح"
    }
}
```

#### GET /me
Retourne les informations de l'utilisateur connecté.

**Headers requis :** Authorization: Bearer {token}

**Réponse succès (200) :**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "Admin ClockIn",
        "email": "<EMAIL>",
        "role": "admin"
    }
}
```

### 2. Pointage

#### POST /check-location
Vérifie si la position est dans le rayon du chantier.

**Headers requis :** Authorization: Bearer {token}

**Paramètres :**
```json
{
    "site_id": 1,
    "latitude": 33.5731,
    "longitude": -7.5898
}
```

**Réponse succès (200) :**
```json
{
    "success": true,
    "data": {
        "is_within_range": true,
        "site": {
            "id": 1,
            "name": "Chantier Casablanca Centre",
            "latitude": 33.5731,
            "longitude": -7.5898
        }
    },
    "message": {
        "en": "Location is within range",
        "fr": "Position dans la zone",
        "ar": "الموقع ضمن النطاق"
    }
}
```

#### POST /save-pointage
Enregistre un pointage.

**Headers requis :** Authorization: Bearer {token}

**Paramètres :**
```json
{
    "user_id": 2,
    "site_id": 1,
    "debut_pointage": "2024-01-15 08:00:00",
    "fin_pointage": "2024-01-15 17:00:00",
    "debut_latitude": 33.5731,
    "debut_longitude": -7.5898,
    "fin_latitude": 33.5731,
    "fin_longitude": -7.5898
}
```

**Réponse succès (201) :**
```json
{
    "success": true,
    "message": {
        "en": "Pointage saved successfully",
        "fr": "Pointage enregistré avec succès",
        "ar": "تم حفظ التوقيت بنجاح"
    },
    "data": {
        "id": 1,
        "user_id": 2,
        "site_id": 1,
        "debut_pointage": "2024-01-15 08:00:00",
        "fin_pointage": "2024-01-15 17:00:00",
        "duree": "09:00:00",
        "debut_latitude": 33.5731,
        "debut_longitude": -7.5898,
        "fin_latitude": 33.5731,
        "fin_longitude": -7.5898,
        "user": {
            "id": 2,
            "name": "Ahmed Benali",
            "email": "<EMAIL>"
        },
        "site": {
            "id": 1,
            "name": "Chantier Casablanca Centre"
        }
    }
}
```

#### GET /pointages (Admin uniquement)
Liste les pointages avec filtres et pagination.

**Headers requis :** Authorization: Bearer {token} (Admin)

**Paramètres optionnels :**
- `date_from` : Date de début (YYYY-MM-DD)
- `date_to` : Date de fin (YYYY-MM-DD)
- `user_id` : ID de l'utilisateur
- `site_id` : ID du site
- `per_page` : Nombre d'éléments par page (défaut: 15)

**Exemple :** `/api/pointages?date_from=2024-01-01&user_id=2&per_page=10`

**Réponse succès (200) :**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "user_id": 2,
            "site_id": 1,
            "debut_pointage": "2024-01-15 08:00:00",
            "fin_pointage": "2024-01-15 17:00:00",
            "duree": "09:00:00",
            "user": {
                "id": 2,
                "name": "Ahmed Benali"
            },
            "site": {
                "id": 1,
                "name": "Chantier Casablanca Centre"
            }
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 1,
        "per_page": 15,
        "total": 1,
        "from": 1,
        "to": 1
    }
}
```

### 3. Gestion des Employés (Admin uniquement)

#### GET /employees
Liste les employés avec recherche et pagination.

**Headers requis :** Authorization: Bearer {token} (Admin)

**Paramètres optionnels :**
- `search` : Recherche par nom ou email
- `role` : Filtrer par rôle (admin/employee)
- `per_page` : Nombre d'éléments par page

**Réponse succès (200) :**
```json
{
    "success": true,
    "data": [
        {
            "id": 2,
            "name": "Ahmed Benali",
            "email": "<EMAIL>",
            "role": "employee",
            "created_at": "2024-01-15 10:00:00"
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 1,
        "per_page": 15,
        "total": 3
    }
}
```

#### POST /employees
Crée un nouvel employé.

**Headers requis :** Authorization: Bearer {token} (Admin)

**Paramètres :**
```json
{
    "name": "Nouveau Employé",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "employee"
}
```

**Réponse succès (201) :**
```json
{
    "success": true,
    "message": {
        "en": "Employee created successfully",
        "fr": "Employé créé avec succès",
        "ar": "تم إنشاء الموظف بنجاح"
    },
    "data": {
        "id": 5,
        "name": "Nouveau Employé",
        "email": "<EMAIL>",
        "role": "employee"
    }
}
```

#### GET /employees/{id}
Récupère les détails d'un employé.

**Headers requis :** Authorization: Bearer {token} (Admin)

**Réponse succès (200) :**
```json
{
    "success": true,
    "data": {
        "id": 2,
        "name": "Ahmed Benali",
        "email": "<EMAIL>",
        "role": "employee",
        "sites": [
            {
                "id": 1,
                "name": "Chantier Casablanca Centre"
            }
        ],
        "pointages": [
            {
                "id": 1,
                "debut_pointage": "2024-01-15 08:00:00",
                "fin_pointage": "2024-01-15 17:00:00"
            }
        ]
    }
}
```

#### PUT /employees/{id}
Met à jour un employé.

**Headers requis :** Authorization: Bearer {token} (Admin)

**Paramètres :**
```json
{
    "name": "Ahmed Benali Modifié",
    "email": "<EMAIL>",
    "role": "employee"
}
```

**Réponse succès (200) :**
```json
{
    "success": true,
    "message": {
        "en": "Employee updated successfully",
        "fr": "Employé mis à jour avec succès",
        "ar": "تم تحديث الموظف بنجاح"
    },
    "data": {
        "id": 2,
        "name": "Ahmed Benali Modifié",
        "email": "<EMAIL>",
        "role": "employee"
    }
}
```

#### DELETE /employees/{id}
Supprime un employé.

**Headers requis :** Authorization: Bearer {token} (Admin)

**Réponse succès (200) :**
```json
{
    "success": true,
    "message": {
        "en": "Employee deleted successfully",
        "fr": "Employé supprimé avec succès",
        "ar": "تم حذف الموظف بنجاح"
    }
}
```

## Codes d'erreur

- **400** : Requête invalide
- **401** : Non authentifié
- **403** : Accès refusé (rôle insuffisant)
- **404** : Ressource non trouvée
- **422** : Erreurs de validation
- **500** : Erreur serveur

### 4. Gestion des Chantiers (Admin uniquement)

#### GET /sites
Liste tous les chantiers.

**Headers requis :** Authorization: Bearer {token} (Admin)

**Réponse succès (200) :**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "Chantier Casablanca Centre",
            "latitude": 33.5731,
            "longitude": -7.5898,
            "users": [
                {
                    "id": 2,
                    "name": "Ahmed Benali"
                }
            ]
        }
    ]
}
```

#### POST /sites
Crée un nouveau chantier.

**Headers requis :** Authorization: Bearer {token} (Admin)

**Paramètres :**
```json
{
    "name": "Nouveau Chantier",
    "latitude": 34.0209,
    "longitude": -6.8416
}
```

**Réponse succès (201) :**
```json
{
    "success": true,
    "message": {
        "en": "Site created successfully",
        "fr": "Site créé avec succès",
        "ar": "تم إنشاء الموقع بنجاح"
    },
    "data": {
        "id": 4,
        "name": "Nouveau Chantier",
        "latitude": 34.0209,
        "longitude": -6.8416
    }
}
```

#### POST /assign-site
Assigne un chantier à des employés.

**Headers requis :** Authorization: Bearer {token} (Admin)

**Paramètres :**
```json
{
    "site_id": 1,
    "user_ids": [2, 3, 4]
}
```

**Réponse succès (200) :**
```json
{
    "success": true,
    "message": {
        "en": "Site assigned successfully",
        "fr": "Site assigné avec succès",
        "ar": "تم تعيين الموقع بنجاح"
    },
    "data": {
        "id": 1,
        "name": "Chantier Casablanca Centre",
        "users": [
            {
                "id": 2,
                "name": "Ahmed Benali"
            },
            {
                "id": 3,
                "name": "Fatima Zahra"
            }
        ]
    }
}
```

#### GET /my-sites
Récupère les chantiers assignés à l'utilisateur connecté.

**Headers requis :** Authorization: Bearer {token}

**Réponse succès (200) :**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "Chantier Casablanca Centre",
            "latitude": 33.5731,
            "longitude": -7.5898
        }
    ]
}
```

### 5. Vérification de Localisation

#### POST /request-verification (Admin uniquement)
Envoie une demande de vérification à un employé.

**Headers requis :** Authorization: Bearer {token} (Admin)

**Paramètres :**
```json
{
    "user_id": 2
}
```

**Réponse succès (200) :**
```json
{
    "success": true,
    "message": {
        "en": "Verification request sent successfully",
        "fr": "Demande de vérification envoyée avec succès",
        "ar": "تم إرسال طلب التحقق بنجاح"
    },
    "data": {
        "user_id": 2,
        "user_name": "Ahmed Benali",
        "requested_at": "2024-01-15 14:30:00"
    }
}
```

#### POST /verify-location
Enregistre la position de l'employé.

**Headers requis :** Authorization: Bearer {token}

**Paramètres :**
```json
{
    "user_id": 2,
    "latitude": 33.5731,
    "longitude": -7.5898,
    "date_heure": "2024-01-15 14:35:00"
}
```

**Réponse succès (201) :**
```json
{
    "success": true,
    "message": {
        "en": "Location verified successfully",
        "fr": "Position vérifiée avec succès",
        "ar": "تم التحقق من الموقع بنجاح"
    },
    "data": {
        "id": 1,
        "user_id": 2,
        "latitude": 33.5731,
        "longitude": -7.5898,
        "date_heure": "2024-01-15 14:35:00",
        "user": {
            "id": 2,
            "name": "Ahmed Benali"
        }
    }
}
```

#### GET /verifications (Admin uniquement)
Récupère l'historique des vérifications.

**Headers requis :** Authorization: Bearer {token} (Admin)

**Paramètres optionnels :**
- `user_id` : ID de l'utilisateur
- `date_from` : Date de début
- `date_to` : Date de fin
- `per_page` : Nombre d'éléments par page

**Réponse succès (200) :**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "user_id": 2,
            "latitude": 33.5731,
            "longitude": -7.5898,
            "date_heure": "2024-01-15 14:35:00",
            "user": {
                "id": 2,
                "name": "Ahmed Benali"
            }
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 1,
        "per_page": 15,
        "total": 1
    }
}
```

## Codes d'erreur

- **400** : Requête invalide
- **401** : Non authentifié
- **403** : Accès refusé (rôle insuffisant)
- **404** : Ressource non trouvée
- **422** : Erreurs de validation
- **500** : Erreur serveur

## Exemple d'erreur de validation (422)

```json
{
    "success": false,
    "message": {
        "en": "Validation failed",
        "fr": "Échec de la validation",
        "ar": "فشل التحقق"
    },
    "errors": {
        "email": [
            "Email is required / Email requis / البريد الإلكتروني مطلوب"
        ],
        "password": [
            "Password must be at least 6 characters / Le mot de passe doit contenir au moins 6 caractères / يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل"
        ]
    }
}
```

## Collection Postman

Vous pouvez importer cette collection Postman pour tester l'API :

```json
{
    "info": {
        "name": "ClockIn API",
        "description": "Collection pour tester l'API ClockIn"
    },
    "variable": [
        {
            "key": "base_url",
            "value": "http://localhost:8000/api"
        },
        {
            "key": "token",
            "value": ""
        }
    ]
}
```

## Notes importantes

1. **Authentification** : Tous les endpoints sauf `/login` nécessitent un token Bearer.
2. **Rôles** : Les endpoints marqués "(Admin uniquement)" nécessitent un utilisateur avec le rôle `admin`.
3. **Géolocalisation** : Le rayon de vérification est fixé à 50 mètres par défaut.
4. **Pagination** : La plupart des listes supportent la pagination avec `per_page` (défaut: 15).
5. **Multilingue** : Tous les messages sont disponibles en anglais, français et arabe.
6. **Logs** : Toutes les actions importantes sont automatiquement loggées pour la traçabilité.
