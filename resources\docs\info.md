# ClockIn API Documentation

Bienvenue dans la documentation de l'API ClockIn, un système complet de gestion de pointage des employés avec géolocalisation.

## 🚀 Fonctionnalités Principales

### Authentification Sécurisée
- Connexion avec email/mot de passe
- Tokens d'authentification Laravel Sanctum
- Gestion des rôles (Admin/Employé)

### Pointage Géolocalisé
- Vérification de position dans un rayon de 50m
- Enregistrement des heures de travail
- Calcul automatique de la durée
- Coordonnées GPS de début/fin

### Gestion des Chantiers
- Création et gestion des sites
- Attribution des employés aux chantiers
- Coordonnées GPS des chantiers

### CRUD Employés
- Création, modification, suppression
- Recherche et filtrage
- Pagination des résultats

### Vérification de Localisation
- Demandes de vérification en temps réel
- Historique des vérifications
- Logs de traçabilité

## 🔐 Authentification

L'API utilise l'authentification Bearer Token via Laravel Sanctum.

### Étapes pour s'authentifier :

1. **Connexion** : Envoyez vos identifiants à `POST /api/login`
2. **Récupération du token** : Copiez le token depuis la réponse
3. **Utilisation** : Incluez le token dans l'en-tête de vos requêtes :
   ```
   Authorization: Bearer {votre_token}
   ```

### Comptes de test disponibles :

- **Admin** : <EMAIL> / password123
- **Employés** :
  - <EMAIL> / password123
  - <EMAIL> / password123
  - <EMAIL> / password123

## 🌍 Support Multilingue

Tous les messages de l'API sont disponibles en trois langues :
- **Anglais** (en)
- **Français** (fr) 
- **Arabe** (ar)

## 📍 Géolocalisation

Le système utilise un rayon de vérification de **50 mètres** autour des chantiers pour valider les pointages.

### Coordonnées des chantiers de test :
- **Casablanca Centre** : 33.5731, -7.5898
- **Rabat Agdal** : 34.0209, -6.8416
- **Marrakech Gueliz** : 31.6295, -7.9811

## 📊 Format des Réponses

Toutes les réponses suivent un format JSON standardisé :

```json
{
    "success": true|false,
    "message": {
        "en": "Message en anglais",
        "fr": "Message en français",
        "ar": "رسالة بالعربية"
    },
    "data": {...}
}
```

## 🔒 Permissions

### Endpoints Publics
- `POST /api/login` - Connexion

### Endpoints Authentifiés
- Tous les autres endpoints nécessitent un token valide

### Endpoints Admin Uniquement
- Gestion des employés (`/api/employees/*`)
- Liste des pointages (`GET /api/pointages`)
- Gestion des chantiers (`/api/sites/*`)
- Demandes de vérification (`POST /api/request-verification`)
- Historique des vérifications (`GET /api/verifications`)

## 📝 Pagination

Les endpoints qui retournent des listes supportent la pagination :

```json
{
    "data": [...],
    "pagination": {
        "current_page": 1,
        "last_page": 5,
        "per_page": 15,
        "total": 67,
        "from": 1,
        "to": 15
    }
}
```

### Paramètres de pagination :
- `per_page` : Nombre d'éléments par page (défaut: 15, max: 100)
- `page` : Numéro de page (défaut: 1)

## ⚠️ Codes d'Erreur

| Code | Description |
|------|-------------|
| 200  | Succès |
| 201  | Créé avec succès |
| 400  | Requête invalide |
| 401  | Non authentifié |
| 403  | Accès refusé (permissions insuffisantes) |
| 404  | Ressource non trouvée |
| 422  | Erreurs de validation |
| 500  | Erreur serveur interne |

## 🛠️ Outils de Test

### Collection Postman
Une collection Postman complète est disponible dans le projet :
`ClockIn_API.postman_collection.json`

### Variables d'environnement
- `base_url` : http://localhost:8001/api
- `token` : Sera automatiquement défini après connexion

## 📞 Support

Pour toute question ou problème :
- Consultez cette documentation
- Vérifiez les logs de l'application
- Contactez l'équipe de développement

---

*Documentation générée automatiquement avec Scribe*
