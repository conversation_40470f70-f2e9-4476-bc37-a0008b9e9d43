<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class GenerateApiDocs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clockin:generate-docs {--force : Force regeneration}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate ClockIn API documentation with Scribe';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Génération de la documentation API ClockIn...');
        $this->newLine();

        // Vérifier si Scribe est installé
        if (!class_exists('\Knuckles\Scribe\ScribeServiceProvider')) {
            $this->error('❌ Scribe n\'est pas installé. Installation en cours...');
            $this->call('composer', ['require', '--dev', 'knuckleswtf/scribe']);
        }

        // Publier la configuration si elle n'existe pas
        if (!File::exists(config_path('scribe.php')) || $this->option('force')) {
            $this->info('📝 Publication de la configuration Scribe...');
            $this->call('vendor:publish', ['--tag' => 'scribe-config', '--force' => true]);
        }

        // Créer les dossiers de documentation
        $this->createDocumentationStructure();

        // Générer la documentation
        $this->info('📚 Génération de la documentation...');
        $this->call('scribe:generate');

        // Copier les fichiers supplémentaires
        $this->copyAdditionalFiles();

        $this->newLine();
        $this->info('✅ Documentation générée avec succès !');
        $this->newLine();
        
        $this->displayAccessInfo();
    }

    /**
     * Create documentation directory structure
     */
    private function createDocumentationStructure()
    {
        $directories = [
            resource_path('docs'),
            resource_path('docs/responses'),
            resource_path('docs/examples'),
            public_path('docs'),
        ];

        foreach ($directories as $directory) {
            if (!File::exists($directory)) {
                File::makeDirectory($directory, 0755, true);
                $this->info("📁 Dossier créé : {$directory}");
            }
        }
    }

    /**
     * Copy additional documentation files
     */
    private function copyAdditionalFiles()
    {
        $this->info('📋 Copie des fichiers supplémentaires...');

        // Créer un fichier README pour la documentation
        $readmeContent = $this->generateReadmeContent();
        File::put(public_path('docs/README.md'), $readmeContent);

        // Créer un fichier de changelog
        $changelogContent = $this->generateChangelogContent();
        File::put(public_path('docs/CHANGELOG.md'), $changelogContent);

        $this->info('✅ Fichiers supplémentaires copiés');
    }

    /**
     * Generate README content for documentation
     */
    private function generateReadmeContent(): string
    {
        return <<<'MD'
# Documentation API ClockIn

Cette documentation a été générée automatiquement avec Scribe.

## Fichiers Disponibles

- **index.html** : Documentation interactive complète
- **collection.json** : Collection Postman pour tester l'API
- **openapi.yaml** : Spécification OpenAPI 3.0
- **README.md** : Ce fichier
- **CHANGELOG.md** : Historique des modifications

## Accès à la Documentation

### En Local
- URL : http://localhost:8000/docs
- Fichier : public/docs/index.html

### En Production
- URL : https://votre-domaine.com/docs

## Mise à Jour

Pour régénérer la documentation :

```bash
php artisan clockin:generate-docs --force
```

## Support

Pour toute question concernant l'API, consultez :
- La documentation interactive
- Les exemples dans la collection Postman
- Le code source des controllers

---

*Généré automatiquement le {{ date }}*
MD;
    }

    /**
     * Generate changelog content
     */
    private function generateChangelogContent(): string
    {
        return <<<'MD'
# Changelog - API ClockIn

## [1.0.0] - {{ date }}

### Ajouté
- ✅ Authentification avec Laravel Sanctum
- ✅ Gestion des rôles (Admin/Employé)
- ✅ Pointage avec géolocalisation
- ✅ CRUD complet des employés
- ✅ Gestion des chantiers
- ✅ Vérification de localisation
- ✅ Logs de traçabilité
- ✅ Support multilingue (EN/FR/AR)
- ✅ Documentation Scribe complète
- ✅ Collection Postman
- ✅ Tests unitaires

### Endpoints Disponibles

#### Authentification
- `POST /api/login` - Connexion
- `POST /api/logout` - Déconnexion
- `GET /api/me` - Profil utilisateur

#### Pointage
- `POST /api/check-location` - Vérifier position
- `POST /api/save-pointage` - Enregistrer pointage
- `GET /api/pointages` - Liste pointages (Admin)

#### Employés (Admin)
- `GET /api/employees` - Liste employés
- `POST /api/employees` - Créer employé
- `GET /api/employees/{id}` - Détails employé
- `PUT /api/employees/{id}` - Modifier employé
- `DELETE /api/employees/{id}` - Supprimer employé

#### Chantiers
- `GET /api/sites` - Liste chantiers (Admin)
- `POST /api/sites` - Créer chantier (Admin)
- `POST /api/assign-site` - Assigner chantier (Admin)
- `GET /api/my-sites` - Mes chantiers

#### Vérification
- `POST /api/request-verification` - Demander vérification (Admin)
- `POST /api/verify-location` - Vérifier position
- `GET /api/verifications` - Historique vérifications (Admin)

### Sécurité
- 🔒 Authentification Bearer Token
- 🔒 Validation des entrées
- 🔒 Protection des routes admin
- 🔒 Logs de traçabilité
- 🔒 Configuration CORS

### Performance
- ⚡ Index de base de données optimisés
- ⚡ Pagination des résultats
- ⚡ Relations Eloquent préchargées
- ⚡ Cache de configuration

---

*Documentation mise à jour automatiquement*
MD;
    }

    /**
     * Display access information
     */
    private function displayAccessInfo()
    {
        $this->table(
            ['Type', 'Accès'],
            [
                ['Documentation HTML', 'http://localhost:8001/docs'],
                ['Collection Postman', 'public/docs/collection.json'],
                ['OpenAPI Spec', 'public/docs/openapi.yaml'],
                ['Fichiers locaux', 'public/docs/'],
            ]
        );

        $this->newLine();
        $this->info('💡 Conseils :');
        $this->line('• Importez collection.json dans Postman pour tester l\'API');
        $this->line('• Utilisez openapi.yaml pour générer des clients SDK');
        $this->line('• La documentation est mise à jour automatiquement');
        $this->newLine();
    }
}
