openapi: 3.0.3
info:
  title: 'ClockIn API Documentation'
  description: "Documentation complète de l'API ClockIn pour la gestion du pointage des employés avec géolocalisation"
  version: 1.0.0
servers:
  -
    url: 'http://localhost:8081'
tags:
  -
    name: Endpoints
    description: ''
components:
  securitySchemes:
    default:
      type: http
      scheme: bearer
      description: 'Vous pouvez récupérer votre token en vous connectant via <code>POST /api/login</code> avec vos identifiants.'
security:
  -
    default: []
paths:
  /api/login:
    post:
      summary: 'Login user and create token'
      operationId: loginUserAndCreateToken
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: 'Must be a valid email address.'
                  example: <EMAIL>
                  nullable: false
                password:
                  type: string
                  description: 'Must be at least 6 characters.'
                  example: 'Z5ij-e/dl4m{o,'
                  nullable: false
              required:
                - email
                - password
      security: []
  /api/logout:
    post:
      summary: 'Logout user (revoke token)'
      operationId: logoutUserrevokeToken
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
  /api/me:
    get:
      summary: 'Get authenticated user'
      operationId: getAuthenticatedUser
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
  /api/check-location:
    post:
      summary: 'Check if location is within site range'
      operationId: checkIfLocationIsWithinSiteRange
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                site_id:
                  type: string
                  description: 'The <code>id</code> of an existing record in the sites table.'
                  example: consequatur
                  nullable: false
                latitude:
                  type: number
                  description: 'Must be between -90 and 90.'
                  example: -90
                  nullable: false
                longitude:
                  type: number
                  description: 'Must be between -180 and 180.'
                  example: -179
                  nullable: false
              required:
                - site_id
                - latitude
                - longitude
      security: []
  /api/save-pointage:
    post:
      summary: 'Save pointage'
      operationId: savePointage
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: string
                  description: 'The <code>id</code> of an existing record in the users table.'
                  example: consequatur
                  nullable: false
                site_id:
                  type: string
                  description: 'The <code>id</code> of an existing record in the sites table.'
                  example: consequatur
                  nullable: false
                debut_pointage:
                  type: string
                  description: 'Must be a valid date.'
                  example: '2025-06-03T22:41:36'
                  nullable: false
                fin_pointage:
                  type: string
                  description: 'Must be a valid date. Must be a date after <code>debut_pointage</code>.'
                  example: '2106-07-03'
                  nullable: true
                debut_latitude:
                  type: number
                  description: 'Must be between -90 and 90.'
                  example: -90
                  nullable: false
                debut_longitude:
                  type: number
                  description: 'Must be between -180 and 180.'
                  example: -179
                  nullable: false
                fin_latitude:
                  type: number
                  description: 'Must be between -90 and 90.'
                  example: -90
                  nullable: true
                fin_longitude:
                  type: number
                  description: 'Must be between -180 and 180.'
                  example: -179
                  nullable: true
              required:
                - user_id
                - site_id
                - debut_pointage
                - debut_latitude
                - debut_longitude
      security: []
  /api/verify-location:
    post:
      summary: 'Verify employee location'
      operationId: verifyEmployeeLocation
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: string
                  description: 'The <code>id</code> of an existing record in the users table.'
                  example: consequatur
                  nullable: false
                latitude:
                  type: number
                  description: 'Must be between -90 and 90.'
                  example: -90
                  nullable: false
                longitude:
                  type: number
                  description: 'Must be between -180 and 180.'
                  example: -179
                  nullable: false
                date_heure:
                  type: string
                  description: 'Must be a valid date.'
                  example: '2025-06-03T22:41:36'
                  nullable: false
              required:
                - user_id
                - latitude
                - longitude
                - date_heure
      security: []
  /api/my-sites:
    get:
      summary: 'Get sites assigned to authenticated user'
      operationId: getSitesAssignedToAuthenticatedUser
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
  /api/pointages:
    get:
      summary: 'Get pointages list (Admin only)'
      operationId: getPointagesListAdminOnly
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
  /api/employees:
    get:
      summary: 'Display a listing of employees'
      operationId: displayAListingOfEmployees
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: 'Store a newly created employee'
      operationId: storeANewlyCreatedEmployee
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 'Must not be greater than 255 characters.'
                  example: vmqeopfuudtdsufvyvddq
                  nullable: false
                email:
                  type: string
                  description: 'Must be a valid email address.'
                  example: <EMAIL>
                  nullable: false
                password:
                  type: string
                  description: 'Must be at least 6 characters.'
                  example: '4[*UyPJ"}6'
                  nullable: false
                role:
                  type: string
                  description: ''
                  example: admin
                  nullable: false
                  enum:
                    - admin
                    - employee
              required:
                - name
                - email
                - password
                - role
      security: []
  '/api/employees/{id}':
    get:
      summary: 'Display the specified employee'
      operationId: displayTheSpecifiedEmployee
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    put:
      summary: 'Update the specified employee'
      operationId: updateTheSpecifiedEmployee
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 'Must not be greater than 255 characters.'
                  example: vmqeopfuudtdsufvyvddq
                  nullable: false
                email:
                  type: string
                  description: 'Must be a valid email address.'
                  example: <EMAIL>
                  nullable: false
                password:
                  type: string
                  description: 'Must be at least 6 characters.'
                  example: '4[*UyPJ"}6'
                  nullable: true
                role:
                  type: string
                  description: ''
                  example: employee
                  nullable: false
                  enum:
                    - admin
                    - employee
              required:
                - name
                - email
                - role
      security: []
    delete:
      summary: 'Remove the specified employee'
      operationId: removeTheSpecifiedEmployee
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: id
        description: 'The ID of the employee.'
        example: consequatur
        required: true
        schema:
          type: string
  /api/sites:
    get:
      summary: 'Get all sites'
      operationId: getAllSites
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
    post:
      summary: 'Store a newly created site'
      operationId: storeANewlyCreatedSite
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 'Must not be greater than 255 characters.'
                  example: vmqeopfuudtdsufvyvddq
                  nullable: false
                latitude:
                  type: number
                  description: 'Must be between -90 and 90.'
                  example: -90
                  nullable: false
                longitude:
                  type: number
                  description: 'Must be between -180 and 180.'
                  example: -180
                  nullable: false
              required:
                - name
                - latitude
                - longitude
      security: []
  /api/assign-site:
    post:
      summary: 'Assign site to employees'
      operationId: assignSiteToEmployees
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                site_id:
                  type: string
                  description: 'The <code>id</code> of an existing record in the sites table.'
                  example: consequatur
                  nullable: false
                user_ids:
                  type: array
                  description: 'The <code>id</code> of an existing record in the users table.'
                  example: null
                  items:
                    type: string
              required:
                - site_id
      security: []
  /api/request-verification:
    post:
      summary: 'Request location verification from an employee (Admin only)'
      operationId: requestLocationVerificationFromAnEmployeeAdminOnly
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: string
                  description: 'The <code>id</code> of an existing record in the users table.'
                  example: consequatur
                  nullable: false
              required:
                - user_id
      security: []
  /api/verifications:
    get:
      summary: 'Get verification history (Admin only)'
      operationId: getVerificationHistoryAdminOnly
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
      tags:
        - Endpoints
      security: []
  '/api/{fallbackPlaceholder}':
    get:
      summary: ''
      operationId: getApiFallbackPlaceholder
      description: ''
      parameters: []
      responses:
        404:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  success: false
                  message:
                    en: 'API endpoint not found'
                    fr: 'Point de terminaison API non trouvé'
                    ar: 'نقطة نهاية API غير موجودة'
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: object
                    properties:
                      en:
                        type: string
                        example: 'API endpoint not found'
                      fr:
                        type: string
                        example: 'Point de terminaison API non trouvé'
                      ar:
                        type: string
                        example: 'نقطة نهاية API غير موجودة'
      tags:
        - Endpoints
      security: []
    parameters:
      -
        in: path
        name: fallbackPlaceholder
        description: ''
        example: 2UZ5i
        required: true
        schema:
          type: string
