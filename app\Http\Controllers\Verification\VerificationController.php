<?php

namespace App\Http\Controllers\Verification;

use App\Http\Controllers\Controller;
use App\Http\Resources\VerificationResource;
use App\Models\Verification;
use App\Models\User;
use App\Models\Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Carbon\Carbon;

/**
 * @group Verification
 *
 * APIs pour la vérification de localisation des employés
 */

class VerificationController extends Controller
{
    /**
     * Demander une vérification
     *
     * Envoie une demande de vérification de localisation à un employé (Admin uniquement).
     *
     * @authenticated
     *
     * @bodyParam user_id integer required L'ID de l'employé à vérifier. Example: 2
     *
     * @response 200 {
     *   "success": true,
     *   "message": {
     *     "en": "Verification request sent successfully",
     *     "fr": "Demande de vérification envoyée avec succès",
     *     "ar": "تم إرسال طلب التحقق بنجاح"
     *   },
     *   "data": {
     *     "user_id": 2,
     *     "user_name": "<PERSON>",
     *     "requested_at": "2024-01-15 14:30:00"
     *   }
     * }
     */
    public function requestVerification(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id'
        ]);
        
        try {
            $user = User::findOrFail($request->user_id);
            
            // Log verification request
            Log::createLog(
                auth()->id(),
                'verification_request',
                'success',
                [
                    'target_user_id' => $user->id,
                    'target_user_name' => $user->name
                ]
            );
            
            // In a real application, you would send a push notification
            // or real-time notification to the employee here
            
            return response()->json([
                'success' => true,
                'message' => [
                    'en' => 'Verification request sent successfully',
                    'fr' => 'Demande de vérification envoyée avec succès',
                    'ar' => 'تم إرسال طلب التحقق بنجاح'
                ],
                'data' => [
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'requested_at' => Carbon::now()->format('Y-m-d H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Failed to send verification request',
                    'fr' => 'Échec de l\'envoi de la demande de vérification',
                    'ar' => 'فشل في إرسال طلب التحقق'
                ]
            ], 500);
        }
    }
    
    /**
     * Verify employee location
     */
    public function verifyLocation(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'date_heure' => 'required|date'
        ]);
        
        try {
            $verification = Verification::create([
                'user_id' => $request->user_id,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'date_heure' => $request->date_heure
            ]);
            
            // Log successful verification
            Log::createLog(
                $request->user_id,
                'location_verification',
                'success',
                [
                    'verification_id' => $verification->id,
                    'latitude' => $request->latitude,
                    'longitude' => $request->longitude
                ]
            );
            
            return response()->json([
                'success' => true,
                'message' => [
                    'en' => 'Location verified successfully',
                    'fr' => 'Position vérifiée avec succès',
                    'ar' => 'تم التحقق من الموقع بنجاح'
                ],
                'data' => new VerificationResource($verification->load('user'))
            ], 201);
            
        } catch (\Exception $e) {
            // Log failed verification
            Log::createLog(
                $request->user_id,
                'location_verification',
                'failed',
                ['error' => $e->getMessage()]
            );
            
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Failed to verify location',
                    'fr' => 'Échec de la vérification de position',
                    'ar' => 'فشل في التحقق من الموقع'
                ]
            ], 500);
        }
    }
    
    /**
     * Get verification history (Admin only)
     */
    public function index(Request $request): JsonResponse
    {
        $query = Verification::with('user');
        
        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }
        
        // Filter by date
        if ($request->has('date_from')) {
            $query->whereDate('date_heure', '>=', $request->date_from);
        }
        
        if ($request->has('date_to')) {
            $query->whereDate('date_heure', '<=', $request->date_to);
        }
        
        // Order by latest first
        $query->orderBy('date_heure', 'desc');
        
        // Paginate results
        $verifications = $query->paginate($request->get('per_page', 15));
        
        return response()->json([
            'success' => true,
            'data' => VerificationResource::collection($verifications->items()),
            'pagination' => [
                'current_page' => $verifications->currentPage(),
                'last_page' => $verifications->lastPage(),
                'per_page' => $verifications->perPage(),
                'total' => $verifications->total(),
                'from' => $verifications->firstItem(),
                'to' => $verifications->lastItem(),
            ]
        ]);
    }
}
