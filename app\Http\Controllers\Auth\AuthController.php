<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\LoginRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Models\Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

/**
 * @group Authentication
 *
 * APIs pour l'authentification des utilisateurs
 */

class AuthController extends Controller
{
    /**
     * Connexion utilisateur
     *
     * Authentifie un utilisateur avec email et mot de passe, retourne un token d'authentification.
     *
     * @bodyParam email string required L'adresse email de l'utilisateur. Example: <EMAIL>
     * @bodyParam password string required Le mot de passe de l'utilisateur. Example: password123
     *
     * @response 200 {
     *   "success": true,
     *   "message": {
     *     "en": "Login successful",
     *     "fr": "Connexion réussie",
     *     "ar": "تم تسجيل الدخول بنجاح"
     *   },
     *   "data": {
     *     "user": {
     *       "id": 1,
     *       "name": "Admin ClockIn",
     *       "email": "<EMAIL>",
     *       "role": "admin"
     *     },
     *     "token": "1|abc123...",
     *     "token_type": "Bearer"
     *   }
     * }
     *
     * @response 401 {
     *   "success": false,
     *   "message": {
     *     "en": "Invalid credentials",
     *     "fr": "Identifiants invalides",
     *     "ar": "بيانات اعتماد غير صحيحة"
     *   }
     * }
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = $request->validated();
        
        $user = User::where('email', $credentials['email'])->first();
        
        if (!$user || !Hash::check($credentials['password'], $user->password)) {
            // Log failed login attempt
            Log::createLog(
                $user?->id,
                'login_attempt',
                'failed',
                ['email' => $credentials['email'], 'reason' => 'invalid_credentials']
            );
            
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Invalid credentials',
                    'fr' => 'Identifiants invalides',
                    'ar' => 'بيانات اعتماد غير صحيحة'
                ]
            ], 401);
        }
        
        // Create token
        $token = $user->createToken('auth_token')->plainTextToken;
        
        // Log successful login
        Log::createLog(
            $user->id,
            'login_attempt',
            'success',
            ['email' => $credentials['email']]
        );
        
        return response()->json([
            'success' => true,
            'message' => [
                'en' => 'Login successful',
                'fr' => 'Connexion réussie',
                'ar' => 'تم تسجيل الدخول بنجاح'
            ],
            'data' => [
                'user' => new UserResource($user),
                'token' => $token,
                'token_type' => 'Bearer'
            ]
        ]);
    }
    
    /**
     * Déconnexion utilisateur
     *
     * Révoque le token d'authentification de l'utilisateur connecté.
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": {
     *     "en": "Logout successful",
     *     "fr": "Déconnexion réussie",
     *     "ar": "تم تسجيل الخروج بنجاح"
     *   }
     * }
     */
    public function logout(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Revoke current token
        $request->user()->currentAccessToken()->delete();
        
        // Log logout
        Log::createLog(
            $user->id,
            'logout_attempt',
            'success'
        );
        
        return response()->json([
            'success' => true,
            'message' => [
                'en' => 'Logout successful',
                'fr' => 'Déconnexion réussie',
                'ar' => 'تم تسجيل الخروج بنجاح'
            ]
        ]);
    }
    
    /**
     * Profil utilisateur
     *
     * Récupère les informations de l'utilisateur actuellement connecté.
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "id": 1,
     *     "name": "Admin ClockIn",
     *     "email": "<EMAIL>",
     *     "role": "admin",
     *     "created_at": "2024-01-15 10:00:00",
     *     "updated_at": "2024-01-15 10:00:00"
     *   }
     * }
     */
    public function me(Request $request): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => new UserResource($request->user())
        ]);
    }
}
