# Configuration Port 8001 - ClockIn API

## ✅ Corrections Effectuées

Tous les fichiers ont été mis à jour pour utiliser le port **8001** au lieu de 8000 :

### 🔧 Fichiers de Configuration
- ✅ **config/scribe.php** - Base URL et Try It Out
- ✅ **.env** - APP_URL mis à jour
- ✅ **ClockIn_API.postman_collection.json** - Variable base_url

### 📚 Documentation
- ✅ **resources/docs/info.md** - URLs et exemples
- ✅ **resources/docs/examples.md** - Tous les exemples curl
- ✅ **API_DOCUMENTATION.md** - Base URL
- ✅ **CLOCKIN_README.md** - URL d'accès
- ✅ **SCRIBE_DOCUMENTATION_GUIDE.md** - URLs de documentation
- ✅ **SCRIBE_SUMMARY.md** - Accès à la documentation

### 🛠️ Scripts
- ✅ **generate_docs.bat/sh** - Messages d'information
- ✅ **update_docs.bat** - URL de documentation
- ✅ **app/Console/Commands/GenerateApiDocs.php** - Table d'accès
- ✅ **start_server.bat/sh** - Nouveaux scripts de démarrage

## 🚀 Utilisation

### Démarrage du Serveur
```bash
# Windows
start_server.bat

# Linux/Mac
chmod +x start_server.sh
./start_server.sh

# Ou directement
php artisan serve --host=localhost --port=8001
```

### Accès aux Services
- **API** : http://localhost:8001/api
- **Documentation** : http://localhost:8001/docs
- **Application** : http://localhost:8001

### Génération de Documentation
```bash
# Windows
generate_docs.bat

# Linux/Mac
./generate_docs.sh

# Commande Artisan
php artisan clockin:generate-docs
```

## 📝 Variables d'Environnement

Assurez-vous que votre fichier `.env` contient :
```env
APP_URL=http://localhost:8001
```

## 🔗 URLs Mises à Jour

### API Endpoints
- Base URL : `http://localhost:8001/api`
- Login : `http://localhost:8001/api/login`
- Pointages : `http://localhost:8001/api/pointages`
- Employés : `http://localhost:8001/api/employees`

### Documentation
- HTML Interactive : `http://localhost:8001/docs`
- Collection Postman : `public/docs/collection.json`
- OpenAPI Spec : `public/docs/openapi.yaml`

### Tests avec Postman
1. Importez `ClockIn_API.postman_collection.json`
2. La variable `base_url` est automatiquement définie sur `http://localhost:8001/api`
3. Testez la connexion avec les comptes fournis

### Tests avec cURL
```bash
# Test de connexion
curl -X POST http://localhost:8001/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Test de vérification de position
curl -X POST http://localhost:8001/api/check-location \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "site_id": 1,
    "latitude": 33.5731,
    "longitude": -7.5898
  }'
```

## ✅ Vérification

Pour vérifier que tout fonctionne correctement :

1. **Démarrez le serveur** : `start_server.bat` ou `./start_server.sh`
2. **Accédez à la documentation** : http://localhost:8001/docs
3. **Testez un endpoint** : Utilisez "Try It Out" dans la documentation
4. **Vérifiez Postman** : Importez et testez la collection

## 🔄 Régénération de Documentation

Si vous modifiez les controllers, régénérez la documentation :
```bash
php artisan clockin:generate-docs --force
```

La documentation sera automatiquement mise à jour avec les bonnes URLs.

---

**Toutes les configurations sont maintenant alignées sur le port 8001** ✅
