{"variable": [{"id": "baseUrl", "key": "baseUrl", "type": "string", "name": "string", "value": "http://localhost:8081"}], "info": {"name": "ClockIn API Documentation", "_postman_id": "d5c1856c-79b1-4daa-a5b5-cf0e3ed2c496", "description": "Documentation complète de l'API ClockIn pour la gestion du pointage des employés avec géolocalisation", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Endpoints", "description": "", "item": [{"name": "Login user and create token", "request": {"url": {"host": "{{baseUrl}}", "path": "api/login", "query": [], "raw": "{{baseUrl}}/api/login"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"email\":\"<EMAIL>\",\"password\":\"Z5ij-e\\/dl4m{o,\"}"}, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": []}, {"name": "Logout user (revoke token)", "request": {"url": {"host": "{{baseUrl}}", "path": "api/logout", "query": [], "raw": "{{baseUrl}}/api/logout"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": []}, {"name": "Get authenticated user", "request": {"url": {"host": "{{baseUrl}}", "path": "api/me", "query": [], "raw": "{{baseUrl}}/api/me"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "Check if location is within site range", "request": {"url": {"host": "{{baseUrl}}", "path": "api/check-location", "query": [], "raw": "{{baseUrl}}/api/check-location"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"site_id\":\"consequatur\",\"latitude\":-90,\"longitude\":-179}"}, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": []}, {"name": "Save pointage", "request": {"url": {"host": "{{baseUrl}}", "path": "api/save-pointage", "query": [], "raw": "{{baseUrl}}/api/save-pointage"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"user_id\":\"consequatur\",\"site_id\":\"consequatur\",\"debut_pointage\":\"2025-06-03T22:41:36\",\"fin_pointage\":\"2106-07-03\",\"debut_latitude\":-90,\"debut_longitude\":-179,\"fin_latitude\":-90,\"fin_longitude\":-179}"}, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": []}, {"name": "Verify employee location", "request": {"url": {"host": "{{baseUrl}}", "path": "api/verify-location", "query": [], "raw": "{{baseUrl}}/api/verify-location"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"user_id\":\"consequatur\",\"latitude\":-90,\"longitude\":-179,\"date_heure\":\"2025-06-03T22:41:36\"}"}, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": []}, {"name": "Get sites assigned to authenticated user", "request": {"url": {"host": "{{baseUrl}}", "path": "api/my-sites", "query": [], "raw": "{{baseUrl}}/api/my-sites"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "Get pointages list (Admin only)", "request": {"url": {"host": "{{baseUrl}}", "path": "api/pointages", "query": [], "raw": "{{baseUrl}}/api/pointages"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "Display a listing of employees", "request": {"url": {"host": "{{baseUrl}}", "path": "api/employees", "query": [], "raw": "{{baseUrl}}/api/employees"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "Store a newly created employee", "request": {"url": {"host": "{{baseUrl}}", "path": "api/employees", "query": [], "raw": "{{baseUrl}}/api/employees"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"vmqeopfuudtdsufvyvddq\",\"email\":\"<EMAIL>\",\"password\":\"4[*UyPJ\\\"}6\",\"role\":\"admin\"}"}, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": []}, {"name": "Display the specified employee", "request": {"url": {"host": "{{baseUrl}}", "path": "api/employees/:id", "query": [], "raw": "{{baseUrl}}/api/employees/:id", "variable": [{"id": "id", "key": "id", "value": "consequatur", "description": "The ID of the employee."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "Update the specified employee", "request": {"url": {"host": "{{baseUrl}}", "path": "api/employees/:id", "query": [], "raw": "{{baseUrl}}/api/employees/:id", "variable": [{"id": "id", "key": "id", "value": "consequatur", "description": "The ID of the employee."}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"vmqeopfuudtdsufvyvddq\",\"email\":\"<EMAIL>\",\"password\":\"4[*UyPJ\\\"}6\",\"role\":\"employee\"}"}, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": []}, {"name": "Remove the specified employee", "request": {"url": {"host": "{{baseUrl}}", "path": "api/employees/:id", "query": [], "raw": "{{baseUrl}}/api/employees/:id", "variable": [{"id": "id", "key": "id", "value": "consequatur", "description": "The ID of the employee."}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": []}, {"name": "Get all sites", "request": {"url": {"host": "{{baseUrl}}", "path": "api/sites", "query": [], "raw": "{{baseUrl}}/api/sites"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "Store a newly created site", "request": {"url": {"host": "{{baseUrl}}", "path": "api/sites", "query": [], "raw": "{{baseUrl}}/api/sites"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"name\":\"vmqeopfuudtdsufvyvddq\",\"latitude\":-90,\"longitude\":-180}"}, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": []}, {"name": "Assign site to employees", "request": {"url": {"host": "{{baseUrl}}", "path": "api/assign-site", "query": [], "raw": "{{baseUrl}}/api/assign-site"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"site_id\":\"consequatur\"}"}, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": []}, {"name": "Request location verification from an employee (Admin only)", "request": {"url": {"host": "{{baseUrl}}", "path": "api/request-verification", "query": [], "raw": "{{baseUrl}}/api/request-verification"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"user_id\":\"consequatur\"}"}, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": []}, {"name": "Get verification history (Admin only)", "request": {"url": {"host": "{{baseUrl}}", "path": "api/verifications", "query": [], "raw": "{{baseUrl}}/api/verifications"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 401, "body": "{\"message\":\"Una<PERSON><PERSON><PERSON><PERSON>.\"}", "name": ""}]}, {"name": "GET api/{fallbackPlaceholder}", "request": {"url": {"host": "{{baseUrl}}", "path": "api/:fallbackPlaceholder", "query": [], "raw": "{{baseUrl}}/api/:fallbackPlaceholder", "variable": [{"id": "fallbackPlaceholder", "key": "fallbackPlaceholder", "value": "2UZ5i", "description": ""}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": "", "auth": {"type": "<PERSON><PERSON><PERSON>"}}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}, {"key": "access-control-allow-origin", "value": "*"}], "code": 404, "body": "{\"success\":false,\"message\":{\"en\":\"API endpoint not found\",\"fr\":\"Point de terminaison API non trouv\\u00e9\",\"ar\":\"\\u0646\\u0642\\u0637\\u0629 \\u0646\\u0647\\u0627\\u064a\\u0629 API \\u063a\\u064a\\u0631 \\u0645\\u0648\\u062c\\u0648\\u062f\\u0629\"}}", "name": ""}]}]}], "auth": {"type": "bearer", "bearer": [{"key": "Authorization", "type": "string"}]}}