# Guide de Documentation API avec Scribe - ClockIn

## 📚 Vue d'ensemble

Ce guide explique comment utiliser et maintenir la documentation API générée avec Scribe pour le projet ClockIn.

## 🚀 Installation et Configuration

### Installation Automatique

Utilisez les scripts fournis pour installer et configurer Scribe :

**Windows :**
```bash
generate_docs.bat
```

**Linux/Mac :**
```bash
chmod +x generate_docs.sh
./generate_docs.sh
```

### Installation Manuelle

1. **Installer Scribe**
```bash
composer require --dev knuckleswtf/scribe
```

2. **Publier la configuration**
```bash
php artisan vendor:publish --tag=scribe-config
```

3. **Générer la documentation**
```bash
php artisan scribe:generate
```

### Commande Personnalisée

Utilisez la commande Artisan personnalisée :
```bash
php artisan clockin:generate-docs
php artisan clockin:generate-docs --force  # Force la régénération
```

## 📁 Structure des Fichiers

```
project/
├── config/scribe.php                    # Configuration Scribe
├── resources/docs/                      # Fichiers de documentation
│   ├── info.md                         # Introduction personnalisée
│   ├── examples.md                     # Exemples d'utilisation
│   ├── custom.css                      # Styles personnalisés
│   └── responses/                      # Exemples de réponses
│       └── auth.json                   # Réponses d'authentification
├── public/docs/                        # Documentation générée
│   ├── index.html                      # Documentation interactive
│   ├── collection.json                 # Collection Postman
│   ├── openapi.yaml                    # Spécification OpenAPI
│   ├── README.md                       # Guide d'utilisation
│   └── CHANGELOG.md                    # Historique des modifications
└── app/Console/Commands/
    └── GenerateApiDocs.php             # Commande personnalisée
```

## 🎯 Annotations Scribe

### Annotations de Base

#### Groupement des Endpoints
```php
/**
 * @group Authentication
 * 
 * APIs pour l'authentification des utilisateurs
 */
class AuthController extends Controller
```

#### Documentation des Méthodes
```php
/**
 * Connexion utilisateur
 * 
 * Authentifie un utilisateur avec email et mot de passe, retourne un token d'authentification.
 * 
 * @authenticated
 * 
 * @bodyParam email string required L'adresse email de l'utilisateur. Example: <EMAIL>
 * @bodyParam password string required Le mot de passe de l'utilisateur. Example: password123
 * 
 * @response 200 {
 *   "success": true,
 *   "data": {
 *     "user": {...},
 *     "token": "1|abc123..."
 *   }
 * }
 */
public function login(LoginRequest $request)
```

### Annotations Avancées

#### Paramètres de Requête
```php
/**
 * @queryParam search string Rechercher par nom ou email. Example: ahmed
 * @queryParam role string Filtrer par rôle (admin/employee). Example: employee
 * @queryParam per_page integer Nombre d'éléments par page. Example: 10
 */
```

#### Paramètres de Corps
```php
/**
 * @bodyParam name string required Le nom complet. Example: Ahmed Benali
 * @bodyParam email string required L'email unique. Example: <EMAIL>
 * @bodyParam latitude number required La latitude. Example: 33.5731
 */
```

#### Paramètres d'URL
```php
/**
 * @urlParam id integer required L'ID de l'employé. Example: 2
 */
```

#### Réponses Multiples
```php
/**
 * @response 200 {
 *   "success": true,
 *   "data": {...}
 * }
 * 
 * @response 401 {
 *   "success": false,
 *   "message": "Unauthorized"
 * }
 * 
 * @response 422 {
 *   "success": false,
 *   "errors": {...}
 * }
 */
```

## 🔧 Configuration Avancée

### Personnalisation de l'Apparence

Le fichier `config/scribe.php` permet de personnaliser :

- **Titre et description** de la documentation
- **URL de base** pour les tests
- **Langues des exemples** de code
- **Groupes d'endpoints** et leur ordre
- **Authentification** et exemples de tokens

### Exemples de Réponses Personnalisés

Créez des fichiers JSON dans `resources/docs/responses/` :

```json
{
  "login_success": {
    "success": true,
    "data": {
      "user": {...},
      "token": "..."
    }
  }
}
```

### Styles Personnalisés

Le fichier `resources/docs/custom.css` contient :
- Couleurs du thème ClockIn
- Styles pour les méthodes HTTP
- Responsive design
- Animations et transitions

## 📖 Utilisation de la Documentation

### Accès à la Documentation

1. **Local** : http://localhost:8000/docs
2. **Fichier** : public/docs/index.html
3. **Production** : https://votre-domaine.com/docs

### Fonctionnalités Interactives

#### Try It Out
- Testez les endpoints directement depuis la documentation
- Authentification automatique avec tokens
- Validation en temps réel

#### Collection Postman
- Importez `public/docs/collection.json` dans Postman
- Variables d'environnement préconfigurées
- Tests automatisés inclus

#### Spécification OpenAPI
- Utilisez `public/docs/openapi.yaml` pour générer des clients SDK
- Compatible avec Swagger UI
- Validation automatique des APIs

## 🔄 Mise à Jour de la Documentation

### Régénération Automatique

La documentation se met à jour automatiquement quand vous :
1. Modifiez les annotations dans les controllers
2. Exécutez `php artisan scribe:generate`
3. Utilisez la commande personnalisée `php artisan clockin:generate-docs`

### Workflow de Développement

1. **Développement** : Ajoutez/modifiez les endpoints
2. **Annotation** : Documentez avec les annotations Scribe
3. **Génération** : Exécutez la commande de génération
4. **Test** : Vérifiez la documentation générée
5. **Commit** : Incluez les fichiers de documentation

### CI/CD Integration

Ajoutez à votre pipeline CI/CD :

```yaml
- name: Generate API Documentation
  run: |
    php artisan clockin:generate-docs
    # Optionnel : déployer vers un serveur de documentation
```

## 🎨 Personnalisation Avancée

### Thèmes Personnalisés

Modifiez `resources/docs/custom.css` pour :
- Changer les couleurs de marque
- Adapter le layout
- Ajouter des animations
- Optimiser pour mobile

### Contenu Personnalisé

Éditez `resources/docs/info.md` pour :
- Ajouter des guides d'utilisation
- Inclure des exemples spécifiques
- Documenter les cas d'usage
- Expliquer l'architecture

### Exemples Interactifs

Créez des exemples dans `resources/docs/examples.md` :
- Workflows complets
- Intégrations tierces
- Cas d'erreur courants
- Bonnes pratiques

## 🚀 Déploiement

### Environnement de Production

1. **Génération** : Exécutez la génération en production
```bash
php artisan clockin:generate-docs --force
```

2. **Serveur Web** : Configurez l'accès à `/docs`
```nginx
location /docs {
    alias /var/www/clockin/public/docs;
    index index.html;
}
```

3. **HTTPS** : Assurez-vous que la documentation est accessible en HTTPS

### Hébergement Séparé

Pour héberger la documentation séparément :

1. Copiez le dossier `public/docs/` vers votre serveur de documentation
2. Configurez un serveur web statique (Nginx, Apache)
3. Mettez à jour l'URL de base dans la configuration

## 📊 Monitoring et Analytics

### Métriques d'Utilisation

Ajoutez Google Analytics ou des outils similaires :

```html
<!-- Dans public/docs/index.html -->
<script>
  // Code de tracking
</script>
```

### Feedback Utilisateurs

Intégrez des outils de feedback :
- Formulaires de contact
- Système de rating
- Commentaires sur les endpoints

## 🔍 Dépannage

### Problèmes Courants

1. **Documentation vide** : Vérifiez les annotations dans les controllers
2. **Erreurs de génération** : Consultez les logs Laravel
3. **Styles cassés** : Vérifiez le fichier CSS personnalisé
4. **Authentification** : Configurez correctement les tokens de test

### Logs et Debug

Activez le debug dans `config/scribe.php` :
```php
'debug' => env('SCRIBE_DEBUG', false),
```

### Support

- Documentation officielle Scribe : https://scribe.knuckles.wtf
- Issues GitHub : https://github.com/knuckleswtf/scribe
- Documentation Laravel : https://laravel.com/docs

## 📝 Bonnes Pratiques

1. **Annotations complètes** : Documentez tous les paramètres
2. **Exemples réalistes** : Utilisez des données cohérentes
3. **Mise à jour régulière** : Régénérez après chaque modification
4. **Tests de documentation** : Vérifiez que les exemples fonctionnent
5. **Feedback utilisateurs** : Collectez et intégrez les retours

---

Cette documentation Scribe vous permet de maintenir une documentation API professionnelle, interactive et toujours à jour pour le projet ClockIn.
