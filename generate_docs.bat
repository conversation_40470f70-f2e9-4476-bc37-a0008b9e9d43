@echo off
echo ========================================
echo     Generation Documentation API Scribe
echo ========================================
echo.

echo 1. Installation de Scribe...
composer require --dev knuckleswtf/scribe
if %errorlevel% neq 0 (
    echo Erreur lors de l'installation de Scribe
    pause
    exit /b 1
)

echo.
echo 2. Publication de la configuration Scribe...
php artisan vendor:publish --tag=scribe-config
if %errorlevel% neq 0 (
    echo Erreur lors de la publication de la configuration
    pause
    exit /b 1
)

echo.
echo 3. Generation de la documentation...
php artisan scribe:generate
if %errorlevel% neq 0 (
    echo Erreur lors de la generation de la documentation
    pause
    exit /b 1
)

echo.
echo ========================================
echo     Documentation generee avec succes !
echo ========================================
echo.
echo La documentation est disponible a :
echo - HTML : public/docs/index.html
echo - URL : http://localhost:8001/docs
echo - Postman : public/docs/collection.json
echo - OpenAPI : public/docs/openapi.yaml
echo.
echo Pour regenerer la documentation :
echo php artisan scribe:generate
echo.
pause
