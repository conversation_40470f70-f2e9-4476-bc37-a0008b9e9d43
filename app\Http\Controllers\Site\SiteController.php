<?php

namespace App\Http\Controllers\Site;

use App\Http\Controllers\Controller;
use App\Http\Requests\SiteRequest;
use App\Http\Resources\SiteResource;
use App\Models\Site;
use App\Models\Assignment;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Sites
 *
 * APIs pour la gestion des chantiers et assignations
 */

class SiteController extends Controller
{
    /**
     * Créer un chantier
     *
     * Crée un nouveau chantier avec ses coordonnées GPS (Admin uniquement).
     *
     * @authenticated
     *
     * @bodyParam name string required Le nom du chantier. Example: Nouveau Chantier Test
     * @bodyParam latitude number required La latitude du chantier. Example: 34.0209
     * @bodyParam longitude number required La longitude du chantier. Example: -6.8416
     *
     * @response 201 {
     *   "success": true,
     *   "message": {
     *     "en": "Site created successfully",
     *     "fr": "Site créé avec succès",
     *     "ar": "تم إنشاء الموقع بنجاح"
     *   },
     *   "data": {
     *     "id": 4,
     *     "name": "Nouveau Chantier Test",
     *     "latitude": 34.0209,
     *     "longitude": -6.8416
     *   }
     * }
     */
    public function store(SiteRequest $request): JsonResponse
    {
        $validated = $request->validated();
        
        try {
            $site = Site::create($validated);
            
            return response()->json([
                'success' => true,
                'message' => [
                    'en' => 'Site created successfully',
                    'fr' => 'Site créé avec succès',
                    'ar' => 'تم إنشاء الموقع بنجاح'
                ],
                'data' => new SiteResource($site)
            ], 201);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Failed to create site',
                    'fr' => 'Échec de la création du site',
                    'ar' => 'فشل في إنشاء الموقع'
                ]
            ], 500);
        }
    }
    
    /**
     * Assign site to employees
     */
    public function assignSite(Request $request): JsonResponse
    {
        $request->validate([
            'site_id' => 'required|exists:sites,id',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id'
        ]);
        
        try {
            $site = Site::findOrFail($request->site_id);
            $userIds = $request->user_ids;
            
            // Remove existing assignments for this site
            Assignment::where('site_id', $site->id)->delete();
            
            // Create new assignments
            foreach ($userIds as $userId) {
                Assignment::create([
                    'site_id' => $site->id,
                    'user_id' => $userId
                ]);
            }
            
            // Load the site with assigned users
            $site->load('users');
            
            return response()->json([
                'success' => true,
                'message' => [
                    'en' => 'Site assigned successfully',
                    'fr' => 'Site assigné avec succès',
                    'ar' => 'تم تعيين الموقع بنجاح'
                ],
                'data' => new SiteResource($site)
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Failed to assign site',
                    'fr' => 'Échec de l\'assignation du site',
                    'ar' => 'فشل في تعيين الموقع'
                ]
            ], 500);
        }
    }
    
    /**
     * Get all sites
     */
    public function index(): JsonResponse
    {
        $sites = Site::with('users')->get();
        
        return response()->json([
            'success' => true,
            'data' => SiteResource::collection($sites)
        ]);
    }
    
    /**
     * Get sites assigned to authenticated user
     */
    public function mySites(): JsonResponse
    {
        $user = auth()->user();
        $sites = $user->sites;
        
        return response()->json([
            'success' => true,
            'data' => SiteResource::collection($sites)
        ]);
    }
}
