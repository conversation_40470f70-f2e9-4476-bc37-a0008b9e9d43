#!/bin/bash

echo "========================================"
echo "     Génération Documentation API Scribe"
echo "========================================"
echo

echo "1. Installation de Scribe..."
composer require --dev knuckleswtf/scribe
if [ $? -ne 0 ]; then
    echo "Erreur lors de l'installation de Scribe"
    exit 1
fi

echo
echo "2. Publication de la configuration Scribe..."
php artisan vendor:publish --tag=scribe-config
if [ $? -ne 0 ]; then
    echo "Erreur lors de la publication de la configuration"
    exit 1
fi

echo
echo "3. Génération de la documentation..."
php artisan scribe:generate
if [ $? -ne 0 ]; then
    echo "Erreur lors de la génération de la documentation"
    exit 1
fi

echo
echo "========================================"
echo "     Documentation générée avec succès !"
echo "========================================"
echo
echo "La documentation est disponible à :"
echo "- HTML : public/docs/index.html"
echo "- URL : http://localhost:8000/docs"
echo "- Postman : public/docs/collection.json"
echo "- OpenAPI : public/docs/openapi.yaml"
echo
echo "Pour régénérer la documentation :"
echo "php artisan scribe:generate"
echo
