# Exemples d'Utilisation - API ClockIn

## 🔐 Authentification

### Connexion Admin
```bash
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Connexion Employé
```bash
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>", 
    "password": "password123"
  }'
```

## 📍 Pointage

### Vérifier la Position
```bash
curl -X POST http://localhost:8000/api/check-location \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "site_id": 1,
    "latitude": 33.5731,
    "longitude": -7.5898
  }'
```

### Enregistrer un Pointage
```bash
curl -X POST http://localhost:8000/api/save-pointage \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 2,
    "site_id": 1,
    "debut_pointage": "2024-01-15 08:00:00",
    "fin_pointage": "2024-01-15 17:00:00",
    "debut_latitude": 33.5731,
    "debut_longitude": -7.5898,
    "fin_latitude": 33.5731,
    "fin_longitude": -7.5898
  }'
```

## 👥 Gestion des Employés (Admin)

### Lister les Employés
```bash
curl -X GET "http://localhost:8000/api/employees?search=ahmed&per_page=10" \
  -H "Authorization: Bearer {admin_token}"
```

### Créer un Employé
```bash
curl -X POST http://localhost:8000/api/employees \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Nouveau Employé",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "employee"
  }'
```

### Modifier un Employé
```bash
curl -X PUT http://localhost:8000/api/employees/2 \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Ahmed Benali Modifié",
    "email": "<EMAIL>",
    "role": "employee"
  }'
```

## 🏗️ Gestion des Chantiers

### Créer un Chantier
```bash
curl -X POST http://localhost:8000/api/sites \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Nouveau Chantier",
    "latitude": 34.0209,
    "longitude": -6.8416
  }'
```

### Assigner un Chantier
```bash
curl -X POST http://localhost:8000/api/assign-site \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "site_id": 1,
    "user_ids": [2, 3, 4]
  }'
```

### Mes Chantiers (Employé)
```bash
curl -X GET http://localhost:8000/api/my-sites \
  -H "Authorization: Bearer {employee_token}"
```

## 🔍 Vérification de Localisation

### Demander une Vérification (Admin)
```bash
curl -X POST http://localhost:8000/api/request-verification \
  -H "Authorization: Bearer {admin_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 2
  }'
```

### Vérifier sa Position (Employé)
```bash
curl -X POST http://localhost:8000/api/verify-location \
  -H "Authorization: Bearer {employee_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 2,
    "latitude": 33.5731,
    "longitude": -7.5898,
    "date_heure": "2024-01-15 14:35:00"
  }'
```

## 📊 Consultation des Données (Admin)

### Liste des Pointages avec Filtres
```bash
curl -X GET "http://localhost:8000/api/pointages?date_from=2024-01-01&user_id=2&per_page=20" \
  -H "Authorization: Bearer {admin_token}"
```

### Historique des Vérifications
```bash
curl -X GET "http://localhost:8000/api/verifications?user_id=2&date_from=2024-01-01" \
  -H "Authorization: Bearer {admin_token}"
```

## 🔄 Workflow Complet

### Scénario : Pointage d'un Employé

1. **Connexion de l'employé**
```bash
TOKEN=$(curl -s -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}' \
  | jq -r '.data.token')
```

2. **Vérification de la position**
```bash
curl -X POST http://localhost:8000/api/check-location \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "site_id": 1,
    "latitude": 33.5731,
    "longitude": -7.5898
  }'
```

3. **Enregistrement du pointage**
```bash
curl -X POST http://localhost:8000/api/save-pointage \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 2,
    "site_id": 1,
    "debut_pointage": "2024-01-15 08:00:00",
    "debut_latitude": 33.5731,
    "debut_longitude": -7.5898
  }'
```

## 🐛 Gestion des Erreurs

### Erreur d'Authentification
```json
{
  "success": false,
  "message": {
    "en": "Invalid credentials",
    "fr": "Identifiants invalides",
    "ar": "بيانات اعتماد غير صحيحة"
  }
}
```

### Erreur de Validation
```json
{
  "success": false,
  "message": {
    "en": "Validation failed",
    "fr": "Échec de la validation",
    "ar": "فشل التحقق"
  },
  "errors": {
    "email": [
      "Email is required / Email requis / البريد الإلكتروني مطلوب"
    ]
  }
}
```

### Position Hors Zone
```json
{
  "success": true,
  "data": {
    "is_within_range": false,
    "site": {
      "id": 1,
      "name": "Chantier Casablanca Centre"
    }
  },
  "message": {
    "en": "Location is outside range",
    "fr": "Position hors zone",
    "ar": "الموقع خارج النطاق"
  }
}
```

## 📱 Intégration Flutter

### Configuration HTTP Client
```dart
class ApiClient {
  static const String baseUrl = 'http://localhost:8000/api';
  static String? authToken;
  
  static Map<String, String> get headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    if (authToken != null) 'Authorization': 'Bearer $authToken',
  };
}
```

### Exemple de Connexion
```dart
Future<bool> login(String email, String password) async {
  final response = await http.post(
    Uri.parse('${ApiClient.baseUrl}/login'),
    headers: ApiClient.headers,
    body: jsonEncode({
      'email': email,
      'password': password,
    }),
  );
  
  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    ApiClient.authToken = data['data']['token'];
    return true;
  }
  return false;
}
```
