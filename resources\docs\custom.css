/* Personnalisation CSS pour la documentation Scribe ClockIn */

:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --success-color: #059669;
    --warning-color: #d97706;
    --error-color: #dc2626;
    --text-color: #1f2937;
    --bg-color: #f9fafb;
    --border-color: #e5e7eb;
}

/* Header personnalisé */
.doc-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.doc-header h1 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.doc-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Navigation */
.sidebar {
    background-color: var(--bg-color);
    border-right: 1px solid var(--border-color);
}

.sidebar .nav-link {
    color: var(--text-color);
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin-bottom: 0.25rem;
    transition: all 0.2s;
}

.sidebar .nav-link:hover {
    background-color: var(--primary-color);
    color: white;
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
}

/* Groupes d'endpoints */
.endpoint-group {
    margin-bottom: 3rem;
}

.endpoint-group h2 {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

/* Endpoints */
.endpoint {
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.endpoint-header {
    background-color: var(--bg-color);
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.endpoint-method {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    font-weight: bold;
    font-size: 0.875rem;
    margin-right: 1rem;
}

.endpoint-method.GET {
    background-color: var(--success-color);
    color: white;
}

.endpoint-method.POST {
    background-color: var(--primary-color);
    color: white;
}

.endpoint-method.PUT {
    background-color: var(--warning-color);
    color: white;
}

.endpoint-method.DELETE {
    background-color: var(--error-color);
    color: white;
}

.endpoint-url {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 1rem;
    color: var(--text-color);
}

.endpoint-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin-top: 0.5rem;
}

.endpoint-description {
    color: #6b7280;
    margin-top: 0.25rem;
}

/* Contenu des endpoints */
.endpoint-content {
    padding: 1.5rem;
}

/* Paramètres */
.parameters-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.parameters-table th,
.parameters-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.parameters-table th {
    background-color: var(--bg-color);
    font-weight: 600;
    color: var(--text-color);
}

.parameter-required {
    color: var(--error-color);
    font-weight: bold;
}

.parameter-optional {
    color: #6b7280;
}

/* Exemples de code */
.code-example {
    background-color: #1f2937;
    color: #f9fafb;
    padding: 1rem;
    border-radius: 0.375rem;
    overflow-x: auto;
    margin: 1rem 0;
}

.code-example pre {
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Réponses */
.response-example {
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    margin: 1rem 0;
}

.response-header {
    background-color: var(--bg-color);
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
}

.response-status-200 {
    border-left: 4px solid var(--success-color);
}

.response-status-201 {
    border-left: 4px solid var(--success-color);
}

.response-status-400,
.response-status-401,
.response-status-403,
.response-status-404,
.response-status-422 {
    border-left: 4px solid var(--error-color);
}

.response-body {
    padding: 1rem;
    background-color: #f8fafc;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.badge-auth {
    background-color: var(--warning-color);
    color: white;
}

.badge-admin {
    background-color: var(--error-color);
    color: white;
}

/* Try it out */
.try-it-out {
    background-color: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.try-it-out h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.try-it-out button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.try-it-out button:hover {
    background-color: var(--secondary-color);
}

/* Messages multilingues */
.multilang-message {
    background-color: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 0.375rem;
    padding: 1rem;
    margin: 1rem 0;
}

.multilang-message h5 {
    color: #92400e;
    margin-bottom: 0.5rem;
}

.multilang-message ul {
    margin: 0;
    padding-left: 1.5rem;
}

.multilang-message li {
    margin-bottom: 0.25rem;
}

/* Responsive */
@media (max-width: 768px) {
    .doc-header h1 {
        font-size: 2rem;
    }
    
    .endpoint-method {
        display: block;
        margin-bottom: 0.5rem;
        margin-right: 0;
    }
    
    .endpoint-url {
        font-size: 0.875rem;
    }
    
    .parameters-table {
        font-size: 0.875rem;
    }
    
    .code-example {
        font-size: 0.75rem;
    }
}

/* Animations */
.endpoint {
    transition: box-shadow 0.2s;
}

.endpoint:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Scrollbar personnalisé */
.code-example::-webkit-scrollbar {
    height: 8px;
}

.code-example::-webkit-scrollbar-track {
    background: #374151;
}

.code-example::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 4px;
}

.code-example::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}
