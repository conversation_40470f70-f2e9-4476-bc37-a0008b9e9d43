<?php

namespace App\Http\Controllers\Pointage;

use App\Http\Controllers\Controller;
use App\Http\Requests\CheckLocationRequest;
use App\Http\Requests\PointageRequest;
use App\Http\Resources\PointageResource;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Pointage
 *
 * APIs pour la gestion du pointage des employés avec géolocalisation
 */

class PointageController extends Controller
{
    /**
     * Vérifier la localisation
     *
     * Vérifie si la position GPS de l'utilisateur est dans un rayon de 50m du chantier spécifié.
     *
     * @authenticated
     *
     * @bodyParam site_id integer required L'ID du chantier à vérifier. Example: 1
     * @bodyParam latitude number required La latitude de la position actuelle. Example: 33.5731
     * @bodyParam longitude number required La longitude de la position actuelle. Example: -7.5898
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "is_within_range": true,
     *     "site": {
     *       "id": 1,
     *       "name": "Chantier Casablanca Centre",
     *       "latitude": 33.5731,
     *       "longitude": -7.5898
     *     }
     *   },
     *   "message": {
     *     "en": "Location is within range",
     *     "fr": "Position dans la zone",
     *     "ar": "الموقع ضمن النطاق"
     *   }
     * }
     */
    public function checkLocation(CheckLocationRequest $request): JsonResponse
    {
        $validated = $request->validated();
        
        $site = Site::findOrFail($validated['site_id']);
        
        $isWithinRange = $site->isWithinRange(
            $validated['latitude'],
            $validated['longitude']
        );
        
        // Log location check
        Log::createLog(
            auth()->id(),
            'location_check',
            $isWithinRange ? 'success' : 'failed',
            [
                'site_id' => $validated['site_id'],
                'latitude' => $validated['latitude'],
                'longitude' => $validated['longitude'],
                'is_within_range' => $isWithinRange
            ]
        );
        
        return response()->json([
            'success' => true,
            'data' => [
                'is_within_range' => $isWithinRange,
                'site' => [
                    'id' => $site->id,
                    'name' => $site->name,
                    'latitude' => (float) $site->latitude,
                    'longitude' => (float) $site->longitude
                ]
            ],
            'message' => [
                'en' => $isWithinRange ? 'Location is within range' : 'Location is outside range',
                'fr' => $isWithinRange ? 'Position dans la zone' : 'Position hors zone',
                'ar' => $isWithinRange ? 'الموقع ضمن النطاق' : 'الموقع خارج النطاق'
            ]
        ]);
    }
    
    /**
     * Enregistrer un pointage
     *
     * Enregistre un nouveau pointage avec les heures de début/fin et les coordonnées GPS.
     * La durée est calculée automatiquement.
     *
     * @authenticated
     *
     * @bodyParam user_id integer required L'ID de l'utilisateur. Example: 2
     * @bodyParam site_id integer required L'ID du chantier. Example: 1
     * @bodyParam debut_pointage string required L'heure de début (format: Y-m-d H:i:s). Example: 2024-01-15 08:00:00
     * @bodyParam fin_pointage string L'heure de fin (format: Y-m-d H:i:s). Example: 2024-01-15 17:00:00
     * @bodyParam debut_latitude number required La latitude de début. Example: 33.5731
     * @bodyParam debut_longitude number required La longitude de début. Example: -7.5898
     * @bodyParam fin_latitude number La latitude de fin. Example: 33.5731
     * @bodyParam fin_longitude number La longitude de fin. Example: -7.5898
     *
     * @response 201 {
     *   "success": true,
     *   "message": {
     *     "en": "Pointage saved successfully",
     *     "fr": "Pointage enregistré avec succès",
     *     "ar": "تم حفظ التوقيت بنجاح"
     *   },
     *   "data": {
     *     "id": 1,
     *     "user_id": 2,
     *     "site_id": 1,
     *     "debut_pointage": "2024-01-15 08:00:00",
     *     "fin_pointage": "2024-01-15 17:00:00",
     *     "duree": "09:00:00",
     *     "debut_latitude": 33.5731,
     *     "debut_longitude": -7.5898,
     *     "fin_latitude": 33.5731,
     *     "fin_longitude": -7.5898,
     *     "user": {
     *       "id": 2,
     *       "name": "Ahmed Benali"
     *     },
     *     "site": {
     *       "id": 1,
     *       "name": "Chantier Casablanca Centre"
     *     }
     *   }
     * }
     */
    public function savePointage(PointageRequest $request): JsonResponse
    {
        $validated = $request->validated();
        
        try {
            $pointage = Pointage::create($validated);
            
            // Log successful pointage
            Log::createLog(
                auth()->id(),
                'pointage_attempt',
                'success',
                ['pointage_id' => $pointage->id]
            );
            
            return response()->json([
                'success' => true,
                'message' => [
                    'en' => 'Pointage saved successfully',
                    'fr' => 'Pointage enregistré avec succès',
                    'ar' => 'تم حفظ التوقيت بنجاح'
                ],
                'data' => new PointageResource($pointage->load(['user', 'site']))
            ], 201);
            
        } catch (\Exception $e) {
            // Log failed pointage
            Log::createLog(
                auth()->id(),
                'pointage_attempt',
                'failed',
                ['error' => $e->getMessage()]
            );
            
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Failed to save pointage',
                    'fr' => 'Échec de l\'enregistrement du pointage',
                    'ar' => 'فشل في حفظ التوقيت'
                ]
            ], 500);
        }
    }

    /**
     * Liste des pointages
     *
     * Récupère la liste de tous les pointages avec filtres et pagination (Admin uniquement).
     *
     * @authenticated
     *
     * @queryParam date_from string Filtrer par date de début (format: Y-m-d). Example: 2024-01-01
     * @queryParam date_to string Filtrer par date de fin (format: Y-m-d). Example: 2024-01-31
     * @queryParam user_id integer Filtrer par ID utilisateur. Example: 2
     * @queryParam site_id integer Filtrer par ID chantier. Example: 1
     * @queryParam per_page integer Nombre d'éléments par page (défaut: 15). Example: 10
     *
     * @response 200 {
     *   "success": true,
     *   "data": [
     *     {
     *       "id": 1,
     *       "user_id": 2,
     *       "site_id": 1,
     *       "debut_pointage": "2024-01-15 08:00:00",
     *       "fin_pointage": "2024-01-15 17:00:00",
     *       "duree": "09:00:00",
     *       "user": {
     *         "id": 2,
     *         "name": "Ahmed Benali"
     *       },
     *       "site": {
     *         "id": 1,
     *         "name": "Chantier Casablanca Centre"
     *       }
     *     }
     *   ],
     *   "pagination": {
     *     "current_page": 1,
     *     "last_page": 1,
     *     "per_page": 15,
     *     "total": 1
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $query = Pointage::with(['user', 'site']);

        // Filter by date
        if ($request->has('date_from')) {
            $query->whereDate('debut_pointage', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('debut_pointage', '<=', $request->date_to);
        }

        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by site
        if ($request->has('site_id')) {
            $query->where('site_id', $request->site_id);
        }

        // Order by latest first
        $query->orderBy('debut_pointage', 'desc');

        // Paginate results
        $pointages = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => PointageResource::collection($pointages->items()),
            'pagination' => [
                'current_page' => $pointages->currentPage(),
                'last_page' => $pointages->lastPage(),
                'per_page' => $pointages->perPage(),
                'total' => $pointages->total(),
                'from' => $pointages->firstItem(),
                'to' => $pointages->lastItem(),
            ]
        ]);
    }
}
