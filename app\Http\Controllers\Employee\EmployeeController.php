<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use App\Http\Requests\EmployeeRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

/**
 * @group Employees
 *
 * APIs pour la gestion CRUD des employés (Admin uniquement)
 */

class EmployeeController extends Controller
{
    /**
     * Liste des employés
     *
     * Récupère la liste de tous les employés avec recherche et pagination (Admin uniquement).
     *
     * @authenticated
     *
     * @queryParam search string Rechercher par nom ou email. Example: ahmed
     * @queryParam role string Filtrer par rôle (admin/employee). Example: employee
     * @queryParam per_page integer Nombre d'éléments par page (défaut: 15). Example: 10
     *
     * @response 200 {
     *   "success": true,
     *   "data": [
     *     {
     *       "id": 2,
     *       "name": "<PERSON>",
     *       "email": "<EMAIL>",
     *       "role": "employee",
     *       "created_at": "2024-01-15 10:00:00"
     *     }
     *   ],
     *   "pagination": {
     *     "current_page": 1,
     *     "last_page": 1,
     *     "per_page": 15,
     *     "total": 3
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $query = User::query();
        
        // Search by name or email
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }
        
        // Filter by role
        if ($request->has('role')) {
            $query->where('role', $request->role);
        }
        
        // Order by name
        $query->orderBy('name');
        
        // Paginate results
        $employees = $query->paginate($request->get('per_page', 15));
        
        return response()->json([
            'success' => true,
            'data' => UserResource::collection($employees->items()),
            'pagination' => [
                'current_page' => $employees->currentPage(),
                'last_page' => $employees->lastPage(),
                'per_page' => $employees->perPage(),
                'total' => $employees->total(),
                'from' => $employees->firstItem(),
                'to' => $employees->lastItem(),
            ]
        ]);
    }
    
    /**
     * Créer un employé
     *
     * Crée un nouvel employé dans le système (Admin uniquement).
     *
     * @authenticated
     *
     * @bodyParam name string required Le nom complet de l'employé. Example: Nouveau Employé
     * @bodyParam email string required L'adresse email unique de l'employé. Example: <EMAIL>
     * @bodyParam password string required Le mot de passe (minimum 6 caractères). Example: password123
     * @bodyParam role string required Le rôle de l'employé (admin/employee). Example: employee
     *
     * @response 201 {
     *   "success": true,
     *   "message": {
     *     "en": "Employee created successfully",
     *     "fr": "Employé créé avec succès",
     *     "ar": "تم إنشاء الموظف بنجاح"
     *   },
     *   "data": {
     *     "id": 5,
     *     "name": "Nouveau Employé",
     *     "email": "<EMAIL>",
     *     "role": "employee"
     *   }
     * }
     */
    public function store(EmployeeRequest $request): JsonResponse
    {
        $validated = $request->validated();
        
        // Hash password
        $validated['password'] = Hash::make($validated['password']);
        
        try {
            $employee = User::create($validated);
            
            return response()->json([
                'success' => true,
                'message' => [
                    'en' => 'Employee created successfully',
                    'fr' => 'Employé créé avec succès',
                    'ar' => 'تم إنشاء الموظف بنجاح'
                ],
                'data' => new UserResource($employee)
            ], 201);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Failed to create employee',
                    'fr' => 'Échec de la création de l\'employé',
                    'ar' => 'فشل في إنشاء الموظف'
                ]
            ], 500);
        }
    }
    
    /**
     * Display the specified employee
     */
    public function show(int $id): JsonResponse
    {
        try {
            $employee = User::with(['sites', 'pointages.site'])->findOrFail($id);
            
            return response()->json([
                'success' => true,
                'data' => new UserResource($employee)
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Employee not found',
                    'fr' => 'Employé non trouvé',
                    'ar' => 'الموظف غير موجود'
                ]
            ], 404);
        }
    }

    /**
     * Update the specified employee
     */
    public function update(EmployeeRequest $request, int $id): JsonResponse
    {
        try {
            $employee = User::findOrFail($id);
            $validated = $request->validated();

            // Hash password if provided
            if (isset($validated['password'])) {
                $validated['password'] = Hash::make($validated['password']);
            }

            $employee->update($validated);

            return response()->json([
                'success' => true,
                'message' => [
                    'en' => 'Employee updated successfully',
                    'fr' => 'Employé mis à jour avec succès',
                    'ar' => 'تم تحديث الموظف بنجاح'
                ],
                'data' => new UserResource($employee->fresh())
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Failed to update employee',
                    'fr' => 'Échec de la mise à jour de l\'employé',
                    'ar' => 'فشل في تحديث الموظف'
                ]
            ], 500);
        }
    }

    /**
     * Remove the specified employee
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $employee = User::findOrFail($id);

            // Prevent deleting the last admin
            if ($employee->isAdmin() && User::where('role', 'admin')->count() <= 1) {
                return response()->json([
                    'success' => false,
                    'message' => [
                        'en' => 'Cannot delete the last admin user',
                        'fr' => 'Impossible de supprimer le dernier utilisateur admin',
                        'ar' => 'لا يمكن حذف آخر مستخدم مدير'
                    ]
                ], 400);
            }

            $employee->delete();

            return response()->json([
                'success' => true,
                'message' => [
                    'en' => 'Employee deleted successfully',
                    'fr' => 'Employé supprimé avec succès',
                    'ar' => 'تم حذف الموظف بنجاح'
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Failed to delete employee',
                    'fr' => 'Échec de la suppression de l\'employé',
                    'ar' => 'فشل في حذف الموظف'
                ]
            ], 500);
        }
    }
}
