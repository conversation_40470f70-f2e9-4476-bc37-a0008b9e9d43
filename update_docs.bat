@echo off
echo ========================================
echo     Mise a jour Documentation API
echo ========================================
echo.

echo Verification des modifications...
git status --porcelain | findstr /C:"app/Http/Controllers" >nul
if %errorlevel% equ 0 (
    echo Modifications detectees dans les controllers.
    echo Regeneration de la documentation...
    
    php artisan clockin:generate-docs --force
    if %errorlevel% neq 0 (
        echo Erreur lors de la regeneration
        pause
        exit /b 1
    )
    
    echo.
    echo Documentation mise a jour avec succes !
    echo.
    echo Voulez-vous commiter les changements ? (o/n)
    set /p commit_choice=
    
    if /i "%commit_choice%"=="o" (
        git add public/docs/
        git commit -m "docs: Update API documentation"
        echo Changements commites.
    )
) else (
    echo Aucune modification detectee dans les controllers.
    echo Regeneration forcee...
    php artisan clockin:generate-docs --force
)

echo.
echo Documentation disponible sur : http://localhost:8000/docs
pause
