#!/bin/bash

echo "========================================"
echo "     Démarrage Serveur ClockIn API"
echo "========================================"
echo

echo "Vérification de la configuration..."
if [ ! -f .env ]; then
    echo "Erreur: Fichier .env non trouvé"
    echo "Exécutez d'abord install.sh"
    exit 1
fi

echo
echo "Démarrage du serveur Laravel sur le port 8001..."
echo
echo "API accessible sur : http://localhost:8001/api"
echo "Documentation sur : http://localhost:8001/docs"
echo
echo "Appuyez sur Ctrl+C pour arrêter le serveur"
echo

php artisan serve --host=localhost --port=8001
