# ClockIn - Application de Pointage des Employés

## Description

ClockIn est une application Laravel professionnelle qui gère les APIs pour une application de pointage des employés. Le système permet de gérer l'authentification, le pointage géolocalisé, les chantiers, et les opérations CRUD des employés avec un focus sur la sécurité et une structure professionnelle.

## Fonctionnalités

### 🔐 Authentification
- Connexion sécurisée avec Laravel Sanctum
- Gestion des rôles (Admin/Employé)
- Tokens d'authentification API

### 📍 Pointage Géolocalisé
- Vérification de position dans un rayon de 50m
- Enregistrement des heures de début/fin
- Calcul automatique de la durée
- Géolocalisation précise

### 🏗️ Gestion des Chantiers
- Création et gestion des sites
- Attribution des employés aux chantiers
- Coordonnées GPS des sites

### 👥 Gestion des Employés (CRUD)
- Création, modification, suppression d'employés
- Recherche et pagination
- Gestion des rôles

### 🔍 Vérification de Localisation
- Demandes de vérification en temps réel
- Historique des vérifications
- Logs de traçabilité

## Installation sur WampServer

### Prérequis
- WampServer avec PHP 8.2+
- MySQL 8.0+
- Composer
- Git

### Étapes d'installation

1. **Cloner le projet**
```bash
cd c:\wamp64\www
git clone [URL_DU_REPO] clockin
cd clockin
```

2. **Installer les dépendances**
```bash
composer install
```

3. **Configuration de l'environnement**
```bash
cp .env.example .env
```

4. **Configurer la base de données**
Modifier le fichier `.env` :
```env
APP_NAME=ClockIn
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=clockin_db
DB_USERNAME=root
DB_PASSWORD=
```

5. **Créer la base de données**
Dans phpMyAdmin, créer une base de données nommée `clockin_db`

6. **Générer la clé d'application**
```bash
php artisan key:generate
```

7. **Exécuter les migrations et seeders**
```bash
php artisan migrate --seed
```

8. **Démarrer le serveur**
```bash
php artisan serve
```

L'application sera accessible sur `http://localhost:8001`

## Comptes de test

Après l'exécution des seeders, vous aurez accès aux comptes suivants :

- **Admin** : <EMAIL> / password123
- **Employés** : 
  - <EMAIL> / password123
  - <EMAIL> / password123
  - <EMAIL> / password123

## Structure de la base de données

### Tables principales

- **users** : Utilisateurs (admin/employés)
- **sites** : Chantiers avec coordonnées GPS
- **pointages** : Enregistrements de pointage
- **verifications** : Vérifications de localisation
- **assignments** : Attribution employés-chantiers
- **logs** : Logs de traçabilité

## API Endpoints

### Authentification
- `POST /api/login` - Connexion
- `POST /api/logout` - Déconnexion
- `GET /api/me` - Profil utilisateur

### Pointage
- `POST /api/check-location` - Vérifier position
- `POST /api/save-pointage` - Enregistrer pointage
- `GET /api/pointages` - Liste pointages (Admin)

### Gestion Employés (Admin)
- `GET /api/employees` - Liste employés
- `POST /api/employees` - Créer employé
- `GET /api/employees/{id}` - Détails employé
- `PUT /api/employees/{id}` - Modifier employé
- `DELETE /api/employees/{id}` - Supprimer employé

### Gestion Chantiers (Admin)
- `GET /api/sites` - Liste chantiers
- `POST /api/sites` - Créer chantier
- `POST /api/assign-site` - Assigner chantier

### Vérification
- `POST /api/request-verification` - Demander vérification (Admin)
- `POST /api/verify-location` - Vérifier position
- `GET /api/verifications` - Historique vérifications (Admin)

## Tests

Exécuter les tests :
```bash
php artisan test
```

Tests disponibles :
- Tests d'authentification
- Tests de pointage
- Tests de gestion des employés
- Tests de géolocalisation

## Sécurité

- Authentification Laravel Sanctum
- Validation des entrées avec FormRequest
- Middleware de protection des routes admin
- Hachage des mots de passe avec bcrypt
- Logs de traçabilité complets
- Protection CORS configurée

## Technologies utilisées

- **Backend** : Laravel 12.x
- **Base de données** : MySQL 8.0+
- **Authentification** : Laravel Sanctum
- **Tests** : PHPUnit
- **Validation** : FormRequest Laravel
- **API Resources** : Formatage des réponses JSON

## Support multilingue

Les messages d'erreur et de succès sont disponibles en :
- Anglais (en)
- Français (fr)
- Arabe (ar)

## Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## Contact

Pour toute question ou support, contactez l'équipe de développement.
